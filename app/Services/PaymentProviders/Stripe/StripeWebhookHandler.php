<?php

namespace App\Services\PaymentProviders\Stripe;

use App\Constants\CheckoutActionTypeEnum;
use App\Constants\OrderStatus;
use App\Constants\PaymentProviderConstants;
use App\Constants\ReceiptStatus;
use App\Constants\SubscriptionStatus;
use App\Constants\SubscriptionType;
use App\Constants\TransactionStatus;
use App\Jobs\SendUserRealtimeNotification;
use App\Mail\Cms\SendCmsMail;
use App\Models\Cart;
use App\Models\Currency;
use App\Models\PaymentProvider;
use App\Models\Receipt;
use App\Models\StripeEvent;
use App\Models\Subscription;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserStripeData;
use App\Services\CartService;
use App\Services\CheckoutService;
use App\Services\ConfigService;
use App\Services\GoogleAnalyticsService;
use App\Services\OrderService;
use App\Services\PaymentProviders\PaymentService;
use App\Services\SubscriptionService;
use App\Services\TransactionService;
use App\Services\ReceiptService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Event;
use Throwable;

class StripeWebhookHandler
{
    public function __construct(
        private SubscriptionService $subscriptionService,
        private CheckoutService $checkoutService,
        private TransactionService $transactionService,
        private OrderService $orderService,
        private PaymentService $paymentService,
        private ConfigService $configService,
        private CartService $cartService,
        private GoogleAnalyticsService $googleAnalyticsService,
        private ReceiptService $receiptService
    ) {}


    public function handleWebhook(Request $request): JsonResponse
    {
        try {
            $event = $this->buildStripeEvent($request);
        } catch (\Throwable $e) {
            return response()->json([
                'message' => 'Invalid payload',
            ], 400);
        }


        $checkAndSaveEvent = true;
        // $checkAndSaveEvent = false;


        $eventId = $event->id;
        if ($checkAndSaveEvent) {

            $savedEvent = StripeEvent::where('event_id', $eventId)->first();
            if ($savedEvent) {
                logger()->info("This webhook has already been processed $event->type $eventId");
                return response()->json([
                    'message' => 'Already processed',
                ], 200);
            }
        }
        DB::transaction(function () use ($event, $checkAndSaveEvent, $eventId) {
            try {
                 $paymentProvider = PaymentProvider::where('slug', PaymentProviderConstants::STRIPE_SLUG)->firstOrFail();
                /** @var StripeProvider $paymentProviderStrategy */
                $paymentProviderStrategy = $this->paymentService->getPaymentProviderBySlug(
                    $paymentProvider->slug
                );
                if ($event->type == 'customer.subscription.created') {
                    if ($event->data->object->id && !empty($event->data->object->id)) {
                        $metadata = $event->data->object->metadata ?? [];
                        // $subscriptionUuid = $subscription->uuid;
                        if ($metadata['checkoutActionType'] == CheckoutActionTypeEnum::SUBSCRIPTION_CREATION->value) {

                            /** @var Subscription $subscription */
                            $subscription = null;
                            $paymentProcessParams = json_decode($metadata['paymentProcessParamsJsonStr'] ?? 'null', true);
                            $accounting = json_decode($metadata['accountingJsonStr'] ?? 'null');
                            $cart = Cart::where('cart_uid', $metadata['cartUid'])->first();
                            $paymentProviderSubscriptionId = $event->data->object->id;
                            $endsAtTmstmp = $this->getSubscriptionEndsAt($event);
                            $startsAtTmstmp = $this->getSubscriptionStartsAt($event);
                            $endsAt = Carbon::createFromTimestampUTC($endsAtTmstmp)->toDateTimeString();
                            $startsAt = Carbon::createFromTimestampUTC($startsAtTmstmp)->toDateTimeString();

                            // The subscription is either created here or in the invoice
                            $subscriptionParams =  [
                                "planSlug" => $metadata['slug'] ?? null,
                                "userId" => $metadata['userId'] ?? null,
                                "paymentProviderSubscriptionId" => $paymentProviderSubscriptionId,
                                "endsAt" => $endsAt,
                                "startsAt" => $startsAt,
                                "newSubscriptionId" => +$metadata['newSubscriptionId'],
                                "cart" => $cart
                            ];
                            $subscription = $this->checkoutService->initSubscriptionCheckoutWithUser(
                                $subscriptionParams
                            );


                            $user = User::find($metadata["userId"]);
                            // Emails
                            $notifEmailVars = $this->generateNotifEmailVars(
                                $user,
                                $subscription,
                                $paymentProcessParams,
                                $accounting
                            );
                            $this->sendUserNotifEmailAfterSubscriptionAction(
                                $user,
                                $metadata['checkoutActionType'],
                                $paymentProcessParams,
                                $notifEmailVars,
                            );
                        }
                         // Convert cart
                        $cart = Cart::where('cart_uid', $metadata['cartUid'])->first();
                        if ($cart) {
                            $cart->markAsConverted(+$metadata['userId']);
                    }
                    }
                } elseif ($event->type == 'customer.subscription.deleted') {
                    $metadata = $event->data->object->metadata ?? [];
                    $stripeSubscriptionId =  $event->data->object->id;
                    $subscription = Subscription::where('payment_provider_subscription_id', $stripeSubscriptionId)->first();
                    if($subscription){
                        $subscription->update([
                            'status' => SubscriptionStatus::CANCELED->value,
                        ]);
                    }
                    if ($event->data->object->cancellation_details->reason == 'payment_failed') {
                        $user =  User::find($metadata["userId"]);
                        $this->sendDeactivatedAfterFailedRenewalUserEmail($user);
                    }
                } elseif ($event->type == 'checkout.session.completed') {
                    $metadata = $event->data->object->metadata ?? [];
                    $paymentProcessParams = json_decode($metadata['paymentProcessParamsJsonStr'] ?? 'null', true);
                    $user = User::find($metadata["userId"]);
                    $accounting = json_decode($metadata['accountingJsonStr'] ?? 'null', true);
                    if (in_array($metadata['checkoutActionType'], [
                        CheckoutActionTypeEnum::SUBSCRIPTION_UPGRADE->value,
                        CheckoutActionTypeEnum::SUBSCRIPTION_DOWNGRADE->value,
                        CheckoutActionTypeEnum::SUBSCRIPTION_SWITCH->value
                    ])) {
                        $newSubscriptionId = isset($metadata['newSubscriptionId']) ? +$metadata['newSubscriptionId'] : null;
                        $newPlanSlug = $metadata['slug'] ?? null;
                        $cart = Cart::where('cart_uid', $metadata['cartUid'])->first();

                        $prevUserSubscription = $this->subscriptionService->findActiveByUserAndSubscriptionId($metadata['userId'], $metadata['prevSubId']);
                        $subscription = $this->subscriptionService->changeStripePlan(
                            $cart,
                            $prevUserSubscription,
                            $paymentProviderStrategy,
                            $newPlanSlug,
                            $paymentProcessParams,
                            $accounting,
                            $event->data->object->id,
                            $newSubscriptionId,

                        );

                        // Emails
                        $notifEmailVars = $this->generateNotifEmailVars(
                            $user,
                            $subscription,
                            $paymentProcessParams,
                            $accounting
                        );
                        $this->sendUserNotifEmailAfterSubscriptionAction(
                            $user,
                            $metadata['checkoutActionType'],
                            $paymentProcessParams,
                            $notifEmailVars,
                        );

                        if (
                            $metadata['checkoutActionType'] == CheckoutActionTypeEnum::SUBSCRIPTION_DOWNGRADE->value
                            && $paymentProcessParams['isRefunded']
                        )
                            $this->sendAfterRefundAdminEmail(
                                $user,
                                $subscription,
                                $accounting
                            );

                    }
                    if ($metadata['checkoutActionType'] == CheckoutActionTypeEnum::ONE_TIME->value) {

                        $currentSubscription = $this->subscriptionService->findActiveByUserAndSubscriptionId($metadata['userId'], $metadata['prevSubId']);
                        $notifEmailVars = $this->generateNotifEmailVars(
                            $user,
                            $currentSubscription,
                            $paymentProcessParams,
                            $accounting
                        );
                        $this->sendUserNotifEmailAfterSubscriptionAction(
                            $user,
                            $metadata['checkoutActionType'],
                            $paymentProcessParams,
                            $notifEmailVars,
                        );
                    }

                    // Convert cart
                    $cart = Cart::where('cart_uid', $metadata['cartUid'])->first();
                    if ($cart) {
                        $cart->markAsConverted(+$metadata['userId']);
                    }

                    // Google analytics send report checkout success
                    $this->googleAnalyticsService->sendPurchaseReport($event->data->object);
                } elseif ($event->type == 'invoice.updated') {
                    $invoice = $event->data->object;
                    $metadata = $invoice->lines->data[0]->metadata ?? [];
                    // The metadata is the same as in the subscription creation
                    $subscription = Subscription::with('last_cart')->where("id",$metadata["newSubscriptionId"] )->first();
                    $cart = $subscription->last_cart; // we take the last cart that has led to this subscription (excluding one time purchases)


                    // create transaction
                    $relevant_statuses = ['paid', 'void', 'uncollectible', 'open'];
                    if($invoice->billing_reason == 'subscription_cycle' && in_array($invoice->status, $relevant_statuses)){
                        $existingInvoice = Transaction::where('payment_provider_transaction_id', $invoice->id)->first();
                        if(!$existingInvoice) {
                            $existingInvoice = $this->transactionService->stripeCreate(
                                $invoice->amount_due,
                                0,
                                $invoice->currency,
                                $invoice->id,
                                $this->mapInvoiceStatusToTransactionStatus($invoice->status),
                                $subscription,
                                $cart,
                                $invoice->number
                            );
                        }

                        if($invoice->status == 'paid'){

                             $this->receiptService->create([
                                "transaction_id" => $existingInvoice->id,
                                "remaining_to_pay" => 0,
                                "amount_already_paid" => 0,
                                "amount_paid" => $existingInvoice->amount,
                                "receipt_status" => ReceiptStatus::SUCCESS->value,
                                "payment_provider_object_id" => $invoice->id
                            ]);
                            $existingInvoice->update([
                                'status' => TransactionStatus::SUCCESS->value,
                            ]);
                        }

                    }
                } elseif ($event->type == 'invoice.payment_succeeded') {

                    $invoiceStripe = $event->data->object;
                    $metadata = $invoiceStripe->lines->data[0]->metadata ?? [];


                    $subscription = $this->subscriptionService->findNewOrActiveByPlanIdAndUser(
                        +$metadata['planId'], $metadata['userId']);

                    $cart = Cart::where('cart_uid', $metadata['cartUid'])->first();
                    // create transaction
                    if($invoiceStripe->billing_reason == 'subscription_create'){
                        $existingInvoice = Transaction::where('payment_provider_transaction_id', $invoiceStripe->id)->first();
                        if($existingInvoice) {
                            $existingInvoice->update([
                                'status' => TransactionStatus::SUCCESS->value,
                            ]);
                        }else {
                            $existingInvoice = $this->transactionService->stripeCreate(
                                $invoiceStripe->amount_due,
                                0,
                                $invoiceStripe->currency,
                                $invoiceStripe->id,
                                TransactionStatus::SUCCESS,
                                $subscription,
                                $cart,
                                $invoiceStripe->number
                            );
                        }
                        $this->receiptService->create([
                            "transaction_id" => $existingInvoice->id,
                            "remaining_to_pay" => 0,
                            "amount_already_paid" => 0,
                            "amount_paid" => $existingInvoice->amount,
                            "receipt_status" => ReceiptStatus::SUCCESS->value,
                            "payment_provider_object_id" => $invoiceStripe->id
                        ]);
                    }

                    // Handle Renewal
                    //  $subscription must not be null
                    if ($invoiceStripe->billing_reason == 'subscription_cycle') {
                        $activeSubscription = $this->subscriptionService->findActiveByPlanSlugAndUser($metadata['slug'], $metadata['userId']);
                        $stripeSubscription = $paymentProviderStrategy->getSubscriptionByStripeId($invoiceStripe->subscription);
                        $endsAtTmstmp = $this->getSubscriptionEndsAtFromObject($stripeSubscription);
                        $startsAtTmstmp = $this->getSubscriptionStartsAtFromObject($stripeSubscription);

                        $this->subscriptionService->renewSubscription([
                            'subscription' => $activeSubscription,
                            'endsAtTmstmp' => $endsAtTmstmp,
                            'startsAtTmstmp' => $startsAtTmstmp,

                            'paymentProviderSubscriptionId' => $invoiceStripe->subscription,
                            'paymentProviderSubscription' => $stripeSubscription,
                        ]);
                    }
                } elseif ($event->type == 'payment_intent.succeeded') {
                    $paymentIntentStripe = $event->data->object;
                    $metadata = $paymentIntentStripe->metadata ?? [];
                    if(in_array($metadata['checkoutActionType'], [
                        CheckoutActionTypeEnum::ONE_TIME->value,
                        CheckoutActionTypeEnum::SUBSCRIPTION_UPGRADE->value,
                        CheckoutActionTypeEnum::SUBSCRIPTION_DOWNGRADE->value,
                        CheckoutActionTypeEnum::SUBSCRIPTION_SWITCH->value,
                    ])){
                        $currentSubscription = $this->subscriptionService->findActiveByUserAndSubscriptionId($metadata['userId'], $metadata['prevSubId']);
                        $cart = Cart::where('cart_uid', $metadata['cartUid'])->first();
                        $transaction = $this->transactionService->stripeCreate(
                                $paymentIntentStripe->amount,
                                0,
                                $paymentIntentStripe->currency,
                                $paymentIntentStripe->id,
                                TransactionStatus::SUCCESS,
                                $currentSubscription,
                                $cart,
                                null
                        );
                        $this->receiptService->create([
                            "transaction_id" => $transaction->id,
                            "remaining_to_pay" => 0,
                            "amount_already_paid" => 0,
                            "amount_paid" => $transaction->amount,
                            "receipt_status" => ReceiptStatus::SUCCESS->value,
                            "payment_provider_object_id" => $paymentIntentStripe->id
                        ]);
                    }
                } elseif ($event->type == 'invoice.payment_failed') {
                    $invoice = $event->data->object;
                    $subscription = $this->subscriptionService->findByPaymentProviderId($paymentProvider, $invoice->subscription);
                    if (!$invoice->next_payment_attempt) {
                        Subscription::where('user_id', $subscription->id)
                            ->where('status', SubscriptionStatus::ACTIVE->value)
                            ->update(['status' => SubscriptionStatus::PAST_DUE->value]);
                        $savedInvoice = Transaction::where('payment_provider_transaction_id', $invoice->id)->first();
                        if($savedInvoice){
                            $savedInvoice->status = TransactionStatus::FAILED->value;
                            $savedInvoice->save();
                        }
                    }

                    $this->sendAdminFailedPaymentEmail($subscription->user, $event);
                    if ($invoice->billing_reason == 'subscription_cycle' && $invoice->attempt_count == 2) {
                        $this->sendFailedRenewalUserEmail($subscription->user);
                    }
                } elseif ($event->type === "refund.created" || $event->type === "refund.updated") {
                    $refundStripe = $event->data->object;
                    if ($refundStripe->status === "succeeded") {
                        $metadata = $refundStripe->metadata;

                        $receipt = Receipt::where('id', $metadata['receiptId'])->lockForUpdate()->first();
                        $transaction = Transaction::where('id', $receipt->transaction_id)->lockForUpdate()->first();


                        if($receipt->receipt_status == ReceiptStatus::PENDING->value){

                            $receipt->receipt_status = ReceiptStatus::SUCCESS->value;
                            $receipt->payment_provider_object_id = $refundStripe->id;
                            $receipt->save();

                            $transaction->remaining_to_pay +=  $refundStripe->amount;
                            if($transaction->remaining_to_pay == 0){
                                $transaction->status = TransactionStatus::REFUNDED;
                            }
                             $transaction->save();
                        }


                    }
                }

                if ($checkAndSaveEvent) {
                    StripeEvent::create([
                        "event_id" => $eventId,
                        "event_type" => $event->type
                    ]);
                }
            } catch (Throwable $e) {
                $this->sendFailedWebhookEventsEmails($event, $e->getTraceAsString());
                throw $e;
            }
        });
        return response()->json();
    }











    private function sendFailedWebhookEventsEmails($event, $errorDetails)
    {
        $emailTemplate = 'core.admin.failed-stripe-event';
        $vars = [
            "appName" => $this->configService->get('app.name'),
            "webhookEventType" => $event->type,
            "webhookData" => json_encode($event->data, JSON_PRETTY_PRINT),
            "errorDetails" => $errorDetails,
            "stripeEventUrl" => str_replace('{{eventId}}', $event->id,  config('app.payment.stripe_event_url'))
        ];

        $recipientsStr =  $this->configService->get('services.stripe.admin_webhook_events_email_recipients');
        if(!$recipientsStr || !strlen($recipientsStr))
            return;

        $recipients = explode(',', $recipientsStr);
        SendCmsMail::make()
            ->to($recipients)
            ->template($emailTemplate)
            ->lang("en")
            ->vars($vars)
            ->send();
    }
    private function sendAdminFailedPaymentEmail(User $customer, $event)
    {

        $invoiceStripeObj = $event->data->object;
        $emailTemplate = 'core.admin.customer-failed-payment';
        $currency = Currency::where('code', strtoupper($invoiceStripeObj->currency))->firstOrFail();


        $emailVars = [
            "appName" => $this->configService->get('app.name'),
            "customerId" => $customer->id,
            "currency" => $currency->symbol,
            "paymentAmount" => $invoiceStripeObj->amount_due / 100,
            "paymentDate" => Carbon::createFromTimestampUTC($invoiceStripeObj->created)->format('d/m/Y'),
            "stripeEventUrl" => str_replace('{{eventId}}', $event->id,  config('app.payment.stripe_event_url'))
        ];
        $recipientsStr =  $this->configService->get('services.stripe.admin_webhook_events_email_recipients');
        if(!$recipientsStr || !strlen($recipientsStr))
            return;

        $recipients = explode(',', $recipientsStr);
        SendCmsMail::make()
                ->to($recipients)
                ->template($emailTemplate)
                ->lang("en")
                ->vars($emailVars)
                ->send();
    }



    public function handleWebhookLegacy(Request $request): JsonResponse
    {
        try {
            $event = $this->buildStripeEvent($request);
        } catch (\Throwable $e) {
            return response()->json([
                'message' => 'Invalid payload',
            ], 400);
        }

        $paymentProvider = PaymentProvider::where('slug', PaymentProviderConstants::STRIPE_SLUG)->firstOrFail();

        // docs on events: https://stripe.com/docs/billing/testing?dashboard-or-api=api

        if (
            $event->type == 'customer.subscription.created' ||
            $event->type == 'customer.subscription.updated' ||
            $event->type == 'customer.subscription.resumed' ||
            $event->type == 'customer.subscription.deleted' ||
            $event->type == 'customer.subscription.paused'
        ) {

            // Log::info('EVENT : ', json_decode(json_encode($event), true));

            // $slug = $event->data->object->metadata->slug ?? null;
            // $user_id = $event->data->object->metadata->user_id ?? null;
            // $current_period_end = $event->data->object->current_period_end ?? null;

            // if (empty($slug) && empty($user_id) && empty($event->data->object->id) && $current_period_end) {
            //     die();
            // }

            // try {
            //     $subscription = $this->checkoutService->initSubscriptionCheckoutWithUser($slug, $user_id, $event->data->object->id, $current_period_end);
            // } catch (SubscriptionCreationNotAllowedException $e) {
            //     Log::error('Tsy mey ee');
            //     // return redirect()->route('checkout.subscription.already-subscribed');
            // }

            if ($event->data->object->id && !empty($event->data->object->id)) {
                // $subscriptionUuid = $subscription->uuid;

                DB::transaction(function () use ($event, $paymentProvider) {

                    // subscription events can arrive at same time, so we need to make sure we lock the subscription to maintain sequential processing
                    $subscription = Subscription::query()->where('payment_provider_subscription_id', $event->data->object->id)->lockForUpdate()->firstOrFail();
                    $stripeSubscriptionStatus = $event->data->object->status;
                    $subscriptionStatus = $this->mapStripeSubscriptionStatusToSubscriptionStatus($stripeSubscriptionStatus);
                    $endsAt = $this->getSubscriptionEndsAt($event);
                    $endsAt = Carbon::createFromTimestampUTC($endsAt)->toDateTimeString();
                    $trialEndsAt = $event->data->object->trial_end ? Carbon::createFromTimestampUTC($event->data->object->trial_end)->toDateTimeString() : null;
                    $cancelledAt = $event->data->object->canceled_at ? Carbon::createFromTimestampUTC($event->data->object->canceled_at)->toDateTimeString() : null;

                    $this->subscriptionService->updateSubscription($subscription, [
                        'type' => SubscriptionType::PAYMENT_PROVIDER_MANAGED,
                        'status' => $subscriptionStatus,
                        'ends_at' => $endsAt,
                        'payment_provider_subscription_id' => $event->data->object->id,
                        'payment_provider_status' => $event->data->object->status,
                        'payment_provider_id' => $paymentProvider->id,
                        'trial_ends_at' => $trialEndsAt,
                        'cancelled_at' => $cancelledAt,
                    ]);
                });
            }
        } elseif ($event->type == 'customer.subscription.trial_will_end') {
            // TODO send email to user

        } elseif ($event->type == 'invoice.created') {
            // Log::info("BOOM");
            $lineItem = $event->data->object->lines->data[0] ?? null;
            // Log::info('Stripe Event JSON:'.$event->data->object->lines->data[0]);
            // Log::info('END Event JSON:'.$lineItem['period']['end']);
            $metadata = $event->data->object->lines->data[0]->metadata ?? [];
            Log::info('Stripe Event JSON:', json_decode(json_encode($event), true));

            if (empty($metadata['slug']) && empty($metadata['user_id']) && empty($event->data->object->subscription) && $lineItem && $lineItem['period']['end']) {
                die();
            }

            $subscription = $this->checkoutService->initSubscriptionCheckoutWithUser(
                [
                    "planSlug" => $metadata['slug'],
                    "userId" => $metadata['user_id'],
                    "paymentProviderSubscriptionId" => $event->data->object->subscription,
                    "endsAt" => Carbon::createFromTimestampUTC($lineItem['period']['end'])->toDateTimeString(),
                ]
            );

            $subscriptionUuid = $subscription->uuid;

            // $subscriptionUuid = $this->getSubscriptionUuidFromInvoiceEvent($event);
            // $subscription = $this->subscriptionService->findByUuidOrFail($subscriptionUuid);
            $currency = Currency::where('code', strtoupper($event->data->object->currency))->firstOrFail();
            $invoiceStatus = $event->data->object->status;

            $discount = $this->sumDiscountAmounts($event->data->object->total_discount_amounts ?? []);
            $tax = $this->sumTaxAmounts($event->data->object->total_tax_amounts ?? []);

            // create transaction


        } elseif (
            $event->type == 'invoice.finalized' ||
            $event->type == 'invoice.paid' ||
            $event->type == 'invoice.updated'
        ) {
            $invoiceStatus = $event->data->object->status;
            $paymentIntent = $event->data->object->payment_intent;
            $fees = $this->calculateFees($paymentIntent);
            // update transaction

            $this->transactionService->updateTransactionByPaymentProviderTxId(
                $event->data->object->id,
                $invoiceStatus,
                $this->mapInvoiceStatusToTransactionStatus($invoiceStatus),
                null,
                null,
                $fees,
            );
        } elseif (
            $event->type == 'invoice.finalization_failed' ||
            $event->type == 'invoice.payment_failed' ||
            $event->type == 'invoice.payment_action_required'
        ) {
            $invoiceStatus = $event->data->object->status;
            // update transaction

            $errorReason = $event->data->object->last_payment_error->message ?? null;

            $this->transactionService->updateTransactionByPaymentProviderTxId(
                $event->data->object->id,
                $invoiceStatus,
                $this->mapInvoiceStatusToTransactionStatus($invoiceStatus),
                $errorReason,
            );

            $subscriptionUuid = $event->data->object->subscription_details->metadata->subscription_uuid;
            $subscription = $this->subscriptionService->findByUuidOrFail($subscriptionUuid);

            $this->subscriptionService->handleInvoicePaymentFailed($subscription);
        } elseif ($event->type == 'customer.updated') {
            $defaultPaymentMethodId = $event->data->object->invoice_settings->default_payment_method;
            $stripeCustomerId = $event->data->object->id;

            UserStripeData::where('stripe_customer_id', $stripeCustomerId)->update([
                'stripe_payment_method_id' => $defaultPaymentMethodId,
            ]);
        } elseif ($event->type == 'payment_intent.succeeded' || $event->type == 'payment_intent.payment_failed') { // order event
            $paymentIntentId = $event->data->object->id;
            $orderUuid = $event->data->object->metadata?->order_uuid;

            if (! empty($orderUuid)) {
                $order = $this->orderService->findByUuidOrFail($orderUuid);
                $fees = $this->calculateFees($paymentIntentId);
                $currency = Currency::where('code', strtoupper($event->data->object->currency))->firstOrFail();

                $transaction = $this->transactionService->getTransactionByPaymentProviderTxId($paymentIntentId);

                $transactionStatus = $event->type == 'payment_intent.succeeded' ? TransactionStatus::SUCCESS : TransactionStatus::FAILED;

                DB::transaction(function () use ($order, $event, $transaction, $transactionStatus, $fees, $currency, $paymentProvider, $paymentIntentId) {
                    if ($transaction) {
                        $this->transactionService->updateTransaction(
                            $transaction,
                            $event->data->object->status,
                            $transactionStatus,
                            null,
                            $event->data->object->amount,
                            $fees,
                        );
                    } else {
                        $this->transactionService->createForOrder(
                            $order,
                            $event->data->object->amount,
                            0,
                            $order->total_discount_amount,
                            $fees,
                            $currency,
                            $paymentProvider,
                            $paymentIntentId,
                            $event->data->object->status,
                            $transactionStatus,
                        );
                    }

                    $orderStatus = $event->type == 'payment_intent.succeeded' ? OrderStatus::SUCCESS : OrderStatus::FAILED;

                    $this->orderService->updateOrder($order, [
                        'status' => $orderStatus->value,
                        'total_amount_after_discount' => $event->data->object->amount,
                        'payment_provider_id' => $paymentProvider->id,
                    ]);
                });
            }
        } elseif ($event->type == 'charge.refunded') { // order event
            $paymentIntentId = $event->data->object->payment_intent;

            $orderUuid = $event->data->object->metadata?->order_uuid;

            if (! empty($orderUuid)) {

                $transaction = $this->transactionService->getTransactionByPaymentProviderTxId($paymentIntentId);

                if ($transaction) {
                    $this->transactionService->updateTransaction(
                        $transaction,
                        'refunded',
                        TransactionStatus::REFUNDED,
                    );

                    if ($transaction->order) {
                        $this->orderService->updateOrder($transaction->order, [
                            'status' => OrderStatus::REFUNDED,
                        ]);
                    }
                }
            }
        } elseif (str_starts_with($event->type, 'charge.dispute.')) { // order event
            $paymentIntentId = $event->data->object->payment_intent;

            $transaction = $this->transactionService->getTransactionByPaymentProviderTxId($paymentIntentId);

            if ($transaction) {
                $this->transactionService->updateTransaction(
                    $transaction,
                    $event->data->object->status,
                    TransactionStatus::DISPUTED,
                );

                if ($transaction->order) {
                    $this->orderService->updateOrder($transaction->order, [
                        'status' => OrderStatus::DISPUTED,
                    ]);
                }
            }
        }

        return response()->json();
    }

    private function mapInvoiceStatusToTransactionStatus(string $invoiceStatus): TransactionStatus
    {
        if ($invoiceStatus == 'paid') {
            return TransactionStatus::SUCCESS;
        }

        if ($invoiceStatus == 'void' or $invoiceStatus == 'uncollectible') {
            return TransactionStatus::FAILED;
        }

        if ($invoiceStatus == 'pending') {
            return TransactionStatus::PENDING;
        }

        if ($invoiceStatus == 'open') {
            return TransactionStatus::NOT_STARTED;
        }

        return TransactionStatus::NOT_STARTED;
    }

    private function mapStripeSubscriptionStatusToSubscriptionStatus(string $stripeSubscriptionStatus)
    {
        if ($stripeSubscriptionStatus == 'active' || $stripeSubscriptionStatus == 'trialing') {
            return SubscriptionStatus::ACTIVE;
        }

        if ($stripeSubscriptionStatus == 'past_due') {
            return SubscriptionStatus::PAST_DUE;
        }

        if ($stripeSubscriptionStatus == 'canceled') {
            return SubscriptionStatus::CANCELED;
        }

        if ($stripeSubscriptionStatus == 'paused') {
            return SubscriptionStatus::PAUSED;
        }

        return SubscriptionStatus::INACTIVE;
    }

    protected function buildStripeEvent(Request $request)
    {
        $this->setupClient();

        return \Stripe\Webhook::constructEvent(
            $request->getContent(),
            $request->header('Stripe-Signature'),
            config('services.stripe.webhook_signing_secret')
        );
    }

    private function setupClient()
    {
        \Stripe\Stripe::setApiKey(config('services.stripe.secret_key'));
    }

    private function sumDiscountAmounts(array $stripeDiscounts): int
    {
        $discount = 0;

        foreach ($stripeDiscounts as $stripeDiscount) {
            $discount += $stripeDiscount->amount;
        }

        return $discount;
    }

    private function sumTaxAmounts(array $stripeTaxes): int
    {
        $tax = 0;

        foreach ($stripeTaxes as $stripeTax) {
            $tax += $stripeTax->amount;
        }

        return $tax;
    }

    protected function calculateFees($paymentIntentId)
    {
        if (! $paymentIntentId) {
            return null;
        }

        $paymentIntent = \Stripe\PaymentIntent::retrieve([
            'id' => $paymentIntentId,
            'expand' => ['latest_charge.balance_transaction'],
        ]);

        return $paymentIntent?->latest_charge?->balance_transaction?->fee ?? 0;
    }

    private function isSuperfluousEvent(Subscription $subscription, string $eventType): bool
    {
        // Stripe events can arrive out of order, so the "updated" event can arrive before the "created" event, so we want to make
        // sure that if the subscription is already active, we need to skip the "created" event.

        if (
            $subscription->type === SubscriptionType::PAYMENT_PROVIDER_MANAGED &&
            $subscription->status == SubscriptionStatus::ACTIVE->value &&
            $eventType == 'customer.subscription.created'
        ) {
            return true;
        }

        return false;
    }

    private function getSubscriptionEndsAt(Event $subscriptionEvent): ?string
    {
        if ($subscriptionEvent->data->object->current_period_end !== null) {
            return $subscriptionEvent->data->object->current_period_end;
        }

        if ($subscriptionEvent->data->object->items !== null) { // change introduced in the Stripe API 2025-03-01.dashboard
            return $subscriptionEvent->data->object->items?->data[0]?->current_period_end;
        }

        return null;
    }

    private function getSubscriptionStartsAt(Event $subscriptionEvent): ?string
    {
        if ($subscriptionEvent->data->object->current_period_start !== null) {
            return $subscriptionEvent->data->object->current_period_start;
        }

        if ($subscriptionEvent->data->object->items !== null) { // change introduced in the Stripe API 2025-03-01.dashboard
            return $subscriptionEvent->data->object->items?->data[0]?->current_period_start;
        }

        return null;
    }

    private function getSubscriptionEndsAtFromObject($subscriptionObj): ?string
    {
        if ($subscriptionObj->current_period_end !== null) {
            return $subscriptionObj->current_period_end;
        }

        if ($subscriptionObj->items !== null) { // change introduced in the Stripe API 2025-03-01.dashboard
            return $subscriptionObj->items?->data[0]?->current_period_end;
        }

        return null;
    }

    private function getSubscriptionStartsAtFromObject($subscriptionObj): ?string
    {
        if ($subscriptionObj->current_period_start !== null) {
            return $subscriptionObj->current_period_start;
        }

        if ($subscriptionObj->items !== null) { // change introduced in the Stripe API 2025-03-01.dashboard
            return $subscriptionObj->items?->data[0]?->current_period_start;
        }

        return null;
    }

    private function getSubscriptionUuidFromInvoiceEvent(Event $invoiceEvent)
    {
        if ($invoiceEvent->data->object->subscription_details !== null) {
            return $invoiceEvent->data->object->subscription_details->metadata->subscription_uuid;
        }

        return $invoiceEvent->data->object->parent->subscription_details->metadata->subscription_uuid;
    }
    private function sendDeactivatedAfterFailedRenewalUserEmail(User $user)
    {
        $userEmailTemplate = 'core.customer.subscription-deactivated-after-failed-payment';

        $lang = $user->settings["base_language"] ?? 'en';
        $vars = [
            'appName' => $this->configService->get('app.name'),
            'customerUsername' => $user->name,
            'subscriptionsUrl' => $this->configService->get("app.dashboard_url")."/subscriptions",
        ];
        SendCmsMail::make()
            ->to($user->email)
            ->template($userEmailTemplate)
            ->lang(app()->getLocale() ?? $lang)
            ->vars($vars)
            ->send();
    }
    private function sendAfterRefundAdminEmail(
        User $user,
        Subscription $newSubscription,
        array $accounting,

        )
    {
         $emailTemplate = 'core.admin.customer-downgraded-with-refund';
         $currentCurrency = config('app.default_currency');
        $currency = Currency::where('code', $currentCurrency)->first();
        $currencySymbol = $currency->symbol;
        $stripeCustomerId = '';
        $stripeData = $user->stripeData();
        if ($stripeData->count() > 0) {
            $stripeData = $stripeData->first();
            $stripeCustomerId = $stripeData->stripe_customer_id;
        }

        $vars = [
            "appName" => $this->configService->get('app.name'),
            "customerId" => $user->id,
            "subscriptionName" => $newSubscription->last_cart->plan_json['product']['name'],
            "customerUsername" => $user->name,
            "currency" => $currencySymbol,
            "refundAmount" => (($accounting && $accounting['total'] < 0) ? -$accounting['total'] : 0)/100,
            "stripeCustomerLink" =>  str_replace('{{customerId}}', $stripeCustomerId,  config('app.payment.stripe_customer_url'))
        ];

        $recipientsStr =  $this->configService->get('services.stripe.admin_webhook_events_email_recipients');
        if(!$recipientsStr || !strlen($recipientsStr))
            return;

        $recipients = explode(',', $recipientsStr);
        SendCmsMail::make()
            ->to($recipients)
            ->template($emailTemplate)
            ->lang("en")
            ->vars($vars)
            ->send();
    }
    private function sendFailedRenewalUserEmail(User $user)
    {
        $userEmailTemplate = 'core.customer.renewal-payment-failed';
        $lang = $user->settings["base_language"] ?? 'en';
        $vars = [
            'appName' => $this->configService->get('app.name'),
            'customerUsername' => $user->name,
            'updateCredentialsUrl' => $this->configService->get("app.dashboard_url").'/subscriptions#payment-credentials-edition',
        ];
        SendCmsMail::make()
            ->to($user->email)
            ->template($userEmailTemplate)
            ->lang(app()->getLocale() ?? $lang)
            ->vars($vars)
            ->send();
    }
    private function sendUserNotifEmailAfterSubscriptionAction(
        User $user,
        string $checkoutActionTypeStr,
        array $paymentProcessParams,
        array $notifEmailVars,
    ) {
        $lang = $user->settings["base_language"] ?? 'en';

        $data = [ 'type' => 'info' ];

        $userEmailTemplate = '';
        if ($checkoutActionTypeStr == CheckoutActionTypeEnum::ONE_TIME->value) {
            $userEmailTemplate = 'core.customer.additional-credit';

            $data['title'] = t('core.customer.additional-credit-title');
            $data['body'] = t('core.customer.additional-credit', [
                'subscriptionName' => $notifEmailVars['subscriptionName']
            ]);
        } else if ($checkoutActionTypeStr == CheckoutActionTypeEnum::SUBSCRIPTION_CREATION->value) {
            $userEmailTemplate = 'core.customer.subscription-created';

            $data['title'] = t('core.customer.subscription-created-title');
            $data['body'] = t('core.customer.subscription-created', [
                'subscriptionName' => $notifEmailVars['subscriptionName'],
                'startedDate' => $notifEmailVars['startedDate'],
                'expiredDate' => $notifEmailVars['expiredDate'],
                'customerId' => $user->id,
            ]);
        } else if ($checkoutActionTypeStr == CheckoutActionTypeEnum::SUBSCRIPTION_UPGRADE->value) {

            if ($paymentProcessParams['isDeferred']) {
                $userEmailTemplate = 'core.customer.subscription-upgraded-deferred';
            } else {
                $userEmailTemplate = 'core.customer.subscription-upgraded-direct';
            }

            $data['title'] = t('core.customer.subscription-upgraded-title');
            $data['body'] = t('core.customer.subscription-upgraded', [
                'subscriptionName' => $notifEmailVars['subscriptionName'],
            ]);
        } else if ($checkoutActionTypeStr == CheckoutActionTypeEnum::SUBSCRIPTION_DOWNGRADE->value) {
            if ($paymentProcessParams['isRefunded']) {
                if ($paymentProcessParams['isDeferred']) {
                    $userEmailTemplate = 'core.customer.subscription-downgraded-with-refund-deferred';
                } else {
                    $userEmailTemplate = 'core.customer.subscription-downgraded-with-refund-direct';
                }

                $data['title'] = t('core.customer.subscription-downgraded-with-refund-title');
                $data['body'] = t('core.customer.subscription-downgraded-with-refund', [
                    'subscriptionName' => $notifEmailVars['subscriptionName'],
                ]);
            } else {
                if ($paymentProcessParams['isDeferred']) {
                    $userEmailTemplate = 'core.customer.subscription-downgraded-without-refund-deferred';
                } else {
                    $userEmailTemplate = 'core.customer.subscription-downgraded-without-refund-direct';
                }

                $data['title'] = t('core.customer.subscription-downgraded-title');
                $data['body'] = t('core.customer.subscription-downgraded', [
                    'subscriptionName' => $notifEmailVars['subscriptionName'],
                    'startedDate' => $notifEmailVars['startedDate'],
                ]);
            }
        }


        SendCmsMail::make()
            ->to($user->email)
            ->template($userEmailTemplate)
            ->lang(app()->getLocale() ?? $lang)
            ->vars($notifEmailVars)
            ->send();

        SendUserRealtimeNotification::dispatch($user, $data)->delay(now()->addSecond(3));
    }

    private function generateNotifEmailVars(
        User $user,
        Subscription $newSubscription, // can be the same old one if direct upgrade
        array $paymentProcessParams,
        ?array $accounting = null
    ): array {

        $currentCurrency = config('app.default_currency');
        $currency = Currency::where('code', $currentCurrency)->first();
        $currencySymbol = $currency->symbol;

        return [
            'appName' => $this->configService->get('app.name'),
            'projectCreditCount' =>   $newSubscription->last_cart->plan_json['product']['credit_count'],

            'projectBalanceCount' => $newSubscription->credit_balance,
            'subscriptionsUrl' => $this->configService->get("app.dashboard_url")."/subscriptions",
            'appLink' => $this->configService->get('app.url'),
            'userCount' => 1, // TODO: add usercount
            'projectCreatedCount' => 0, // TODO: integrate with project modules
            'customerUsername' => $user->name,
            'subscriptionName' => $newSubscription->last_cart->plan_json['product']['name'],
            'startedDate' =>  $newSubscription->starts_at ? Carbon::parse($newSubscription->starts_at)->format('d/m/Y') : null,
            'expiredDate' => $newSubscription->ends_at ? Carbon::parse($newSubscription->ends_at)->format('d/m/Y') : null,
            'projectBillingPeriodIntervalAdv' => $newSubscription->last_cart->plan_json['interval']['adverb'],
            'currency' => $currencySymbol,
            'refundAmount' =>( ($accounting && $accounting['total'] < 0) ? -$accounting['total'] : 0)/100
        ];
    }
}

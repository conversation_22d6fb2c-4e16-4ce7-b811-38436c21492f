<?php

namespace App\Livewire\Admin;

use App\Models\Receipt;
use App\Models\Transaction;
use App\Services\InvoiceService;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;

class TransactionReceiptsTable extends Component implements HasForms, HasActions
{
    use InteractsWithForms;
    use InteractsWithActions;
    private InvoiceService $invoiceService;

    public Transaction $transaction;
    public function boot(InvoiceService $invoiceService)
    {
        $this->invoiceService = $invoiceService;
    }
    public function getActions(): array
    {
        return [
            $this->generateReceiptAction(),
            $this->generateExampleAction(),
        ];
    }

    public function mount(Transaction $transaction): void
    {
        $this->transaction = $transaction;
    }

    public function render()
    {
        return view('livewire.admin.transaction-receipts-table');
    }
    public function generateExampleAction(): Action
    {
        return Action::make('generate-Example')
            ->label('EXAMPLE')
            ->modalHeading('Example Modal')
            ->modalSubmitActionLabel('Submit')
            ->form([
                Forms\Components\TextInput::make('email')
                    ->label('Email')
                    ->email()
                    ->required(),
            ])
            ->action(function (array $data) {
                // For demo: just show the email
                \Filament\Notifications\Notification::make()
                    ->title('Example action completed')
                    ->body("Email: {$data['email']}")
                    ->success()
                    ->send();
            });
    }
    public function generateReceiptAction(): Action
    {


        return Action::make('generateReceipt')
            ->label(__('View Receipt'))
            ->modal()
            ->modalHeading(__('Generate Receipt'))
            ->modalDescription(__('Please review and customize the receipt details before generating the document.'))
            ->modalSubmitActionLabel(__('Generate Receipt'))
            ->mountUsing(function (Form $form, array $arguments) {
                // Logging arguments

                // Find receipt by UUID
                $receipt = Receipt::where('uuid', $arguments['receipt_uuid'])->firstOrFail();
                $transaction = Transaction::with(['cart'])->where('id', $receipt->transaction_id)->firstOrFail();
                $customerJson = $transaction->customer_json;


                // Get user data from the transaction's subscription


                $form->fill([
                    'email' =>  $customerJson['email'] ?? '',
                    'displayed_name' =>  $this->invoiceService->getInvoiceCustomerName($customerJson),
                    'phone' => $customerJson['address']['phone'] ?? '',
                    'address_line_1' => $customerJson['address']['address_line_1'] ?? '',
                    'address_line_2' =>  $customerJson['address']['address_line_2'] ?? '',
                    'city' =>  $customerJson['address']['city'] ?? '',
                    'country' => isset($customerJson['address']['country']) ?   $customerJson['address']['country']['name'] : '',
                ]);
            })
            ->form([
                Forms\Components\Section::make(__('Contact Information'))
                    ->schema([
                        Forms\Components\TextInput::make('email')
                            ->label(__('Email'))
                            ->email()
                            ->required(),

                        Forms\Components\TextInput::make('displayed_name')
                            ->label(__('Displayed Name'))
                            ->required(),

                        Forms\Components\TextInput::make('phone')
                            ->label(__('Phone'))
                            ->tel(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('Address Information'))
                    ->schema([
                        Forms\Components\TextInput::make('address_line_1')
                            ->label(__('Address Line 1'))
                            ->required(),

                        Forms\Components\TextInput::make('address_line_2')
                            ->label(__('Address Line 2')),

                        Forms\Components\TextInput::make('city')
                            ->label(__('City'))
                            ->required(),

                        Forms\Components\TextInput::make('country')
                            ->label(__('Country'))
                            ->required(),
                    ])
                    ->columns(2),
            ])
            ->action(function (array $data, array $arguments) {
               // redirects to generate invoice with the form values as json in the query string
                return redirect()->route('invoice_receipt.generate', [
                    'docType' => 'receipt',
                    'uuid' =>  $arguments['receipt_uuid'],
                    'customData' => json_encode($data)
                ]);
            });
    }
}
